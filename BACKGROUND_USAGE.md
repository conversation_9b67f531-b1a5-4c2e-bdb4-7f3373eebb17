# JSON翻译器后台运行指南

## 问题分析

原脚本在使用 `nohup &` 后台运行时可能遇到的问题：

1. **标准输出缓冲问题**: Python的print函数在非终端环境下可能被缓冲，导致输出延迟或丢失
2. **信号处理问题**: 后台进程可能无法正确处理中断信号
3. **日志记录问题**: 缺乏专门的日志文件，难以监控进程状态
4. **进程管理问题**: 难以跟踪和管理后台进程

## 解决方案

### 1. 修复后的脚本特性

修复后的 `json_translator_batch.py` 包含以下改进：

- **日志系统**: 添加了完整的日志记录功能，支持文件日志
- **安全输出**: 使用 `safe_print()` 函数处理输出，避免管道破裂错误
- **信号处理**: 添加了信号处理器，支持优雅退出
- **缓冲区管理**: 强制刷新输出缓冲区，确保实时输出
- **后台检测**: 自动检测是否在终端中运行，调整输出策略

### 2. 后台运行脚本

提供了专门的后台运行脚本 `run_translator_background.sh`：

- **进程管理**: 自动管理PID文件，防止重复运行
- **日志管理**: 自动生成带时间戳的日志文件
- **状态监控**: 提供启动、停止、状态检查功能
- **错误处理**: 完善的错误处理和恢复机制

## 使用方法

### 方法1: 使用后台运行脚本（推荐）

```bash
# 启动翻译器
./run_translator_background.sh --input /path/to/input --output /path/to/output --model qwen-mt-plus --debug

# 检查状态
./run_translator_background.sh status

# 停止翻译器
./run_translator_background.sh stop

# 重启翻译器
./run_translator_background.sh restart
```

### 方法2: 直接使用nohup（修复后的脚本）

```bash
# 后台运行，指定日志文件
nohup python3 json_translator_batch.py \
    --input /path/to/input \
    --output /path/to/output \
    --model qwen-mt-plus \
    --debug \
    --log-file translator.log > translator.log 2>&1 &

# 获取进程ID
echo $! > translator.pid

# 监控日志
tail -f translator.log

# 停止进程
kill $(cat translator.pid)
```

### 方法3: 使用systemd服务（生产环境推荐）

创建服务文件 `/etc/systemd/system/json-translator.service`:

```ini
[Unit]
Description=JSON Translator Service
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/script/directory
ExecStart=/usr/bin/python3 json_translator_batch.py --input /path/to/input --output /path/to/output --model qwen-mt-plus --log-file /var/log/json-translator.log
Restart=on-failure
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable json-translator
sudo systemctl start json-translator
sudo systemctl status json-translator
```

## 监控和调试

### 1. 日志监控

```bash
# 实时查看日志
tail -f json_translator.log

# 查看最近的错误
grep -i error json_translator.log | tail -10

# 查看翻译进度
grep -i "正在翻译\|翻译完成" json_translator.log | tail -10
```

### 2. 进程监控

```bash
# 检查进程是否运行
ps aux | grep json_translator_batch.py

# 查看进程资源使用
top -p $(cat translator.pid)

# 查看进程详细信息
cat /proc/$(cat translator.pid)/status
```

### 3. 性能监控

```bash
# 监控文件处理进度
watch -n 5 'find /path/to/output -name "*.json" | wc -l'

# 监控磁盘使用
df -h /path/to/output

# 监控网络连接（API调用）
netstat -an | grep :443
```

## 故障排除

### 1. 常见问题

**问题**: 进程启动后立即退出
```bash
# 解决方案: 检查日志文件
cat json_translator.log
# 常见原因: API密钥错误、路径不存在、权限问题
```

**问题**: 翻译速度很慢
```bash
# 解决方案: 检查网络连接和API限制
# 调整请求间隔时间（脚本中的sleep时间）
```

**问题**: 内存使用过高
```bash
# 解决方案: 分批处理大文件，或增加系统内存
# 监控内存使用: watch -n 1 'ps -p $(cat translator.pid) -o pid,ppid,cmd,%mem,%cpu'
```

### 2. 调试技巧

```bash
# 启用详细调试
python3 json_translator_batch.py --debug --input test_dir --output output_dir

# 测试单个文件
python3 json_translator_batch.py --input single_file_dir --output test_output --debug

# 检查API连接
python3 -c "import dashscope; dashscope.api_key='your_key'; print('API连接正常')"
```

## 最佳实践

1. **使用专门的日志目录**: 将日志文件放在专门的目录中，便于管理
2. **定期清理日志**: 设置日志轮转，避免日志文件过大
3. **监控磁盘空间**: 确保输出目录有足够的空间
4. **备份重要数据**: 在翻译前备份原始文件
5. **测试小批量**: 先用小批量文件测试，确认配置正确
6. **监控API配额**: 注意API调用次数和费用限制

## 性能优化建议

1. **选择合适的模型**: 翻译专用模型(qwen-mt-plus)通常更快更准确
2. **调整请求间隔**: 根据API限制调整sleep时间
3. **并行处理**: 对于大量文件，可以考虑分目录并行处理
4. **缓存利用**: 脚本已内置缓存机制，避免重复翻译
5. **网络优化**: 确保网络连接稳定，考虑使用CDN或代理
