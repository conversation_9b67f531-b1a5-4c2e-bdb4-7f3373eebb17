#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JSON 翻译器（批量版）：将整个目录中的 JSON 文件翻译成中文，保留原有结构和键名
使用阿里云通义千问大模型 API 进行翻译
"""

import json
import time
import argparse
import os
import glob
import sys
import traceback
from typing import Dict, Any, Set, Optional
import dashscope
from dashscope.aigc.generation import Generation

# 设置API密钥
API_KEY = "sk-a42b37159a09413ebeb7dcb7c3dade9d"
dashscope.api_key = API_KEY

# 不需要翻译的字段
EXCLUDE_KEYS = {"type", "style", "level", "src", "url", "scraped_at"}

# 需要保留原字段并创建中文字段的字段（其他字段将直接翻译替换）
KEEP_ORIGINAL_KEYS = {"category_path"}

# 翻译缓存，避免重复翻译相同内容
translation_cache = {}
# 失败缓存，记录翻译失败的文本，避免重复尝试
failed_cache = set()
# 调试模式
DEBUG = False

def log_debug(message: str, level: str = "INFO"):
    """
    调试日志函数

    Args:
        message: 日志消息
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
    """
    if DEBUG:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")

def is_chinese_text(text: str) -> bool:
    """
    检查文本是否包含中文字符

    Args:
        text: 待检查的文本

    Returns:
        是否包含中文字符
    """
    import re
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def is_translation_model(model: str) -> bool:
    """
    判断是否为翻译专用模型

    Args:
        model: 模型名称

    Returns:
        是否为翻译专用模型
    """
    translation_models = {'qwen-mt-turbo', 'qwen-mt-plus'}
    return model.lower() in translation_models

def get_translation_options(source_lang: str = "English", target_lang: str = "Chinese") -> dict:
    """
    获取翻译配置参数

    Args:
        source_lang: 源语言名称
        target_lang: 目标语言名称

    Returns:
        翻译配置字典
    """
    return {
        "source_lang": source_lang,
        "target_lang": target_lang
    }

def translate_text(text: str, model: str) -> str:
    """
    使用通义千问模型翻译文本

    Args:
        text: 需要翻译的文本
        model: 使用的模型名称

    Returns:
        翻译后的文本
    """
    log_debug(f"开始翻译文本: '{text}' (长度: {len(text)})")

    # 对空字符串或只有空白的字符串，无需翻译
    if not text or text.isspace():
        log_debug(f"跳过空白文本: '{text}'")
        return text

    # 如果已经是中文，跳过翻译
    if is_chinese_text(text):
        log_debug(f"检测到中文文本，跳过翻译: '{text}'")
        return text

    # 检查是否在失败缓存中
    if text in failed_cache:
        log_debug(f"文本在失败缓存中，跳过翻译: '{text}'", "WARNING")
        return text

    # 检查缓存中是否已有该文本的翻译
    if text in translation_cache:
        cached_result = translation_cache[text]
        log_debug(f"从缓存获取翻译: '{text}' -> '{cached_result}'")
        # 验证缓存的翻译结果是否有效
        if cached_result != text and is_chinese_text(cached_result):
            return cached_result
        else:
            log_debug(f"缓存的翻译结果无效，重新翻译: '{cached_result}'", "WARNING")
            # 从缓存中移除无效结果
            del translation_cache[text]
    
    try:
        # 构建提示词，要求翻译成中文
        prompt = f"请将以下英文文本翻译成自然、准确的中文。只需返回翻译结果，不要添加任何额外解释：\n\n{text}"
        
        # 添加请求超时和重试机制
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                log_debug(f"第 {retry_count + 1} 次尝试翻译: '{text}' (使用模型: {model})")

                # 调用通义千问大模型API进行翻译，使用简化版本
                try:
                    log_debug(f"调用简化API: model={model}, prompt长度={len(prompt)}")

                    # 判断是否为翻译专用模型
                    if is_translation_model(model):
                        log_debug(f"检测到翻译专用模型: {model}, 使用翻译API")
                        # 翻译专用模型使用messages格式
                        response = Generation.call(
                            model=model,
                            messages=[
                                {"role": "user", "content": text}
                            ],
                            result_format='message',
                            translation_options=get_translation_options("en", "zh")
                        )
                    else:
                        log_debug(f"使用通用文本生成模型: {model}")
                        # 通用模型使用prompt方式
                        response = Generation.call(
                            model=model,
                            prompt=prompt,
                            temperature=0.1,
                            top_p=0.8,
                            max_tokens=2048,
                            stream=False
                        )
                    log_debug(f"API响应类型: {type(response)}")
                    
                    # 处理API响应
                    if hasattr(response, 'output'):
                        # DashScope响应对象
                        output = response.output
                        if is_translation_model(model):
                            # 翻译专用模型的响应处理
                            if hasattr(output, 'choices') and output.choices and len(output.choices) > 0:
                                translated_text = output.choices[0].message.content.strip()
                                log_debug(f"翻译模型API成功: '{text}' -> '{translated_text}'")
                            elif hasattr(output, 'text') and output.text:
                                # 备用字段
                                translated_text = output.text.strip()
                                log_debug(f"翻译模型API成功(备用字段): '{text}' -> '{translated_text}'")
                            else:
                                log_debug(f"翻译模型API响应格式异常: {response}", "ERROR")
                                translated_text = text
                        else:
                            # 通用模型的响应处理
                            if hasattr(output, 'text') and output.text:
                                translated_text = output.text.strip()
                                log_debug(f"通用模型API成功: '{text}' -> '{translated_text}'")
                            else:
                                log_debug(f"通用模型API响应格式异常: {response}", "ERROR")
                                translated_text = text
                    elif isinstance(response, dict):
                        # 字典格式响应（备用处理）
                        if is_translation_model(model):
                            if 'output' in response and 'choices' in response['output'] and len(response['output']['choices']) > 0:
                                translated_text = response['output']['choices'][0]['message']['content'].strip()
                                log_debug(f"翻译模型API成功(字典): '{text}' -> '{translated_text}'")
                            else:
                                log_debug(f"翻译模型API字典响应格式异常: {response}", "ERROR")
                                translated_text = text
                        else:
                            if 'output' in response and 'text' in response['output']:
                                translated_text = response['output']['text'].strip()
                                log_debug(f"通用模型API成功(字典): '{text}' -> '{translated_text}'")
                            else:
                                log_debug(f"通用模型API字典响应格式异常: {response}", "ERROR")
                                translated_text = text
                    else:
                        log_debug(f"API响应类型异常: {type(response)}", "ERROR")
                        translated_text = text

                except Exception as e:
                    log_debug(f"简化API调用失败: {e}, 尝试备用方法", "WARNING")
                    
                    # 备用方法：使用完整参数版本
                    log_debug(f"调用备用API: model={model}, timeout=30s")

                    if is_translation_model(model):
                        log_debug(f"备用方法使用翻译专用模型: {model}")
                        # 翻译专用模型的备用调用方式
                        response = Generation.call(
                            model=model,
                            messages=[
                                {"role": "user", "content": text}
                            ],
                            result_format='message',
                            translation_options=get_translation_options("en", "zh"),
                            timeout=30
                        )
                    else:
                        log_debug(f"备用方法使用通用模型: {model}")
                        # 通用模型的备用调用方式
                        response = Generation.call(
                            model=model,
                            prompt=prompt,
                            result_format='message',
                            max_tokens=2048,
                            temperature=0.1,
                            timeout=30,
                            stream=False
                        )
                    log_debug(f"备用API响应类型: {type(response)}")

                    # 提取翻译结果 - 正确处理API响应
                    if isinstance(response, str):
                        # 如果响应是字符串，直接使用
                        translated_text = response.strip()
                        log_debug(f"备用API字符串响应: '{translated_text}'")
                    elif hasattr(response, 'output'):
                        # DashScope响应对象
                        output = response.output
                        if is_translation_model(model):
                            # 翻译专用模型的响应处理
                            if hasattr(output, 'choices') and output.choices and len(output.choices) > 0:
                                translated_text = output.choices[0].message.content.strip()
                                log_debug(f"备用翻译模型API成功(choices): '{translated_text}'")
                            elif hasattr(output, 'text') and output.text:
                                translated_text = output.text.strip()
                                log_debug(f"备用翻译模型API成功(text): '{translated_text}'")
                            else:
                                log_debug(f"备用翻译模型API无法提取结果: {response}", "ERROR")
                                translated_text = text
                        else:
                            # 通用模型的响应处理
                            if hasattr(output, 'text') and output.text:
                                translated_text = output.text.strip()
                                log_debug(f"备用通用模型API成功(text): '{translated_text}'")
                            elif hasattr(output, 'choices') and output.choices and len(output.choices) > 0:
                                translated_text = output.choices[0].message.content.strip()
                                log_debug(f"备用通用模型API成功(choices): '{translated_text}'")
                            else:
                                log_debug(f"备用通用模型API无法提取结果: {response}", "ERROR")
                                translated_text = text
                    elif isinstance(response, dict):
                        # 如果响应是字典（备用处理）
                        if 'output' in response:
                            output = response['output']
                            if is_translation_model(model):
                                # 翻译专用模型的响应处理
                                if 'choices' in output and len(output['choices']) > 0:
                                    translated_text = output['choices'][0]['message']['content'].strip()
                                    log_debug(f"备用翻译模型API成功(字典choices): '{translated_text}'")
                                elif 'text' in output:
                                    translated_text = output['text'].strip()
                                    log_debug(f"备用翻译模型API成功(字典text): '{translated_text}'")
                                else:
                                    log_debug(f"备用翻译模型API字典无法提取结果: {response}", "ERROR")
                                    translated_text = text
                            else:
                                # 通用模型的响应处理
                                if 'text' in output:
                                    translated_text = output['text'].strip()
                                    log_debug(f"备用通用模型API成功(字典text): '{translated_text}'")
                                elif 'choices' in output and len(output['choices']) > 0:
                                    translated_text = output['choices'][0]['message']['content'].strip()
                                    log_debug(f"备用通用模型API成功(字典choices): '{translated_text}'")
                                else:
                                    log_debug(f"备用通用模型API字典无法提取结果: {response}", "ERROR")
                                    translated_text = text
                        else:
                            log_debug(f"备用API响应字典中没有output字段: {response}", "ERROR")
                            translated_text = text
                    else:
                        log_debug(f"备用API未处理的响应类型: {type(response)}", "ERROR")
                        translated_text = text

                # 验证翻译结果
                if translated_text and translated_text != text:
                    # 检查翻译结果是否包含中文
                    if is_chinese_text(translated_text):
                        log_debug(f"翻译成功: '{text}' -> '{translated_text}'")
                        # 保存到缓存
                        translation_cache[text] = translated_text
                        # 添加请求间隔，避免触发API限制
                        time.sleep(10.0)
                        return translated_text
                    else:
                        log_debug(f"翻译结果不包含中文，可能翻译失败: '{translated_text}'", "WARNING")
                        # 不缓存可疑的翻译结果，继续重试
                        translated_text = text
                else:
                    log_debug(f"翻译结果与原文相同，可能翻译失败: '{translated_text}'", "WARNING")
                    
            except (KeyboardInterrupt, SystemExit):
                # 允许用户中断程序
                log_debug("程序被用户中断", "INFO")
                raise
            except Exception as e:
                # 捕获API调用异常，记录并重试
                log_debug(f"API调用出错: {e}", "ERROR")
                if DEBUG:
                    import traceback
                    log_debug(f"详细错误信息: {traceback.format_exc()}", "ERROR")
                retry_count += 1
                if retry_count <= max_retries:
                    log_debug(f"正在尝试第 {retry_count} 次重试，等待2秒...", "WARNING")
                    time.sleep(2)  # 重试前等待2秒
                else:
                    log_debug(f"重试 {max_retries} 次后仍未成功，将文本加入失败缓存: '{text}'", "ERROR")
                    # 将失败的文本加入失败缓存
                    failed_cache.add(text)
                    return text

        # 如果达到最大重试次数后仍未成功
        log_debug(f"达到最大重试次数，将文本加入失败缓存: '{text}'", "ERROR")
        failed_cache.add(text)
        return text
        
    except (KeyboardInterrupt, SystemExit):
        # 允许用户中断程序
        log_debug("程序被用户中断", "INFO")
        raise
    except Exception as e:
        log_debug(f"翻译过程中出现未捕获的错误: {e}", "ERROR")
        # 如果是认证错误，提供更详细的错误信息
        if "authentication" in str(e).lower():
            log_debug("可能是API密钥无效或已过期，请检查密钥配置", "ERROR")
        # 将失败的文本加入失败缓存
        failed_cache.add(text)
        return text

def should_translate(key: str, exclude_keys: Set[str]) -> bool:
    """
    判断该字段是否应该被翻译
    
    Args:
        key: 字段名
        exclude_keys: 排除翻译的字段集合
        
    Returns:
        布尔值，表示是否应该翻译该字段
    """
    return key not in exclude_keys

def translate_json(data: Any, exclude_keys: Set[str], model: str, parent_key: Optional[str] = None) -> Any:
    """
    递归翻译JSON数据中的文本

    Args:
        data: JSON数据，可能是字典、列表或基本类型
        exclude_keys: 排除翻译的字段集合
        model: 使用的模型名称
        parent_key: 父级键名，用于判断列表内容是否需要翻译

    Returns:
        翻译后的数据，保留原有结构，并为需要翻译的字段创建对应的中文字段
    """
    # 处理字典类型
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            # 递归处理嵌套结构，传递当前键名作为父级键名
            translated_value = translate_json(value, exclude_keys, model, key)

            # 判断字段的处理方式
            if not should_translate(key, exclude_keys):
                # 不需要翻译的字段，直接保留
                result[key] = translated_value
            elif key in KEEP_ORIGINAL_KEYS:
                # 需要保留原字段并创建中文字段的字段
                result[key] = translated_value

                # 创建对应的中文字段
                if isinstance(translated_value, str):
                    cn_key = f"{key}_cn"
                    result[cn_key] = translate_text(translated_value, model)
                elif isinstance(translated_value, list) and all(isinstance(item, str) for item in translated_value):
                    cn_key = f"{key}_cn"
                    result[cn_key] = [translate_text(item, model) for item in translated_value]
            else:
                # 其他需要翻译的字段，直接翻译替换
                if isinstance(translated_value, str):
                    result[key] = translate_text(translated_value, model)
                elif isinstance(translated_value, list) and all(isinstance(item, str) for item in translated_value):
                    result[key] = [translate_text(item, model) for item in translated_value]
                else:
                    result[key] = translated_value

        return result

    # 处理列表类型
    elif isinstance(data, list):
        # 对于特殊字段中的列表元素，如果是字符串类型，需要翻译
        if parent_key == "list_items" or parent_key == "tags" or parent_key == "headers" or parent_key == "key_points":
            log_debug(f"处理特殊列表字段: {parent_key}, 包含 {len(data)} 个项目")
            result = []
            for i, item in enumerate(data):
                # 如果列表元素是字符串且父键不在排除列表中，直接翻译
                if isinstance(item, str) and should_translate(parent_key, exclude_keys):
                    log_debug(f"翻译列表项 {i+1}/{len(data)}: '{item}' (字段: {parent_key})")
                    translated_item = translate_text(item, model)
                    result.append(translated_item)
                    log_debug(f"列表项翻译结果: '{item}' -> '{translated_item}'")
                else:
                    # 否则递归处理
                    log_debug(f"递归处理列表项 {i+1}/{len(data)} (类型: {type(item)})")
                    result.append(translate_json(item, exclude_keys, model, parent_key))
            log_debug(f"列表字段 {parent_key} 处理完成，共 {len(result)} 个项目")
            return result
        else:
            # 其他列表字段，递归处理每个元素
            return [translate_json(item, exclude_keys, model, parent_key) for item in data]

    # 返回基本类型
    else:
        return data

def process_file(input_file: str, output_file: str, exclude_keys: Set[str], model: str, delete_source: bool = False) -> bool:
    """
    处理单个JSON文件：读取、翻译、保存

    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
        exclude_keys: 排除翻译的字段集合
        model: 使用的模型名称
        delete_source: 是否在翻译完成后删除原文件

    Returns:
        处理是否成功
    """
    try:
        # 创建输出目录（如果不存在）
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 读取JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        log_debug(f"开始处理文件: {input_file} -> {output_file}")
        print(f"正在翻译: {input_file} -> {output_file}")

        # 翻译JSON数据
        translated_data = translate_json(data, exclude_keys, model)

        # 保存翻译后的JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, ensure_ascii=False, indent=2)

        log_debug(f"翻译完成，输出文件已保存: {output_file}")

        # 如果启用了删除原文件选项，则删除原始文件
        if delete_source:
            try:
                os.remove(input_file)
                log_debug(f"原始文件已删除: {input_file}")
                print(f"✓ 已删除原文件: {input_file}")
            except Exception as delete_error:
                log_debug(f"删除原文件失败: {input_file}, 错误: {delete_error}", "ERROR")
                print(f"⚠ 警告: 删除原文件失败: {input_file} - {delete_error}")
                # 删除失败不影响翻译成功的状态

        return True

    except Exception as e:
        log_debug(f"处理文件失败: {input_file}, 错误: {e}", "ERROR")
        print(f"处理文件 {input_file} 时出错: {e}")
        if DEBUG:
            print(traceback.format_exc())
        return False

def process_directory(input_dir: str, output_dir: str, exclude_keys: Set[str], model: str, pattern: str = "**/*.json", delete_source: bool = False) -> None:
    """
    处理目录中的所有JSON文件

    Args:
        input_dir: 输入目录路径
        output_dir: 输出目录路径
        exclude_keys: 排除翻译的字段集合
        model: 使用的模型名称
        pattern: 文件匹配模式
        delete_source: 是否在翻译完成后删除原文件
    """
    # 确保目录路径以斜杠结束
    input_dir = os.path.normpath(input_dir)
    output_dir = os.path.normpath(output_dir)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有匹配的JSON文件
    search_pattern = os.path.join(input_dir, pattern)
    json_files = glob.glob(search_pattern, recursive=True)
    
    if not json_files:
        print(f"在 {input_dir} 中没有找到JSON文件")
        return
    
    total_files = len(json_files)
    success_count = 0
    deleted_count = 0

    print(f"找到 {total_files} 个JSON文件需要翻译")
    if delete_source:
        print("⚠ 警告: 启用了删除原文件模式，翻译完成后将删除原始文件")

    # 处理每个文件
    try:
        for idx, input_file in enumerate(json_files, 1):
            # 计算相对路径，保持目录结构
            rel_path = os.path.relpath(input_file, input_dir)
            output_file = os.path.join(output_dir, rel_path)

            # 显示进度
            print(f"[{idx}/{total_files}] 正在翻译: {input_file} -> {output_file}")

            # 处理文件
            if process_file(input_file, output_file, exclude_keys, model, delete_source):
                success_count += 1
                if delete_source and not os.path.exists(input_file):
                    deleted_count += 1

        # 输出完成统计
        print(f"\n翻译完成! 共翻译了 {len(translation_cache)} 个不同的字符串")
        print(f"成功处理: {success_count}/{total_files} 个文件")
        if delete_source:
            print(f"已删除原文件: {deleted_count} 个")
        print(f"翻译结果保存在: {output_dir}")
    
    except KeyboardInterrupt:
        print("\n\n翻译过程被用户中断！")
        print(f"部分翻译结果已保存在: {output_dir}")
        print(f"已成功处理: {success_count} 个文件")
        if delete_source and deleted_count > 0:
            print(f"已删除原文件: {deleted_count} 个")
        print(f"已翻译 {len(translation_cache)} 个不同的字符串")

# 全局变量用于存储命令行参数
args = None

def main():
    """主函数，处理命令行参数并执行翻译"""
    parser = argparse.ArgumentParser(description='批量JSON翻译器 - 将整个目录中的JSON文件翻译成中文')
    parser.add_argument('--input', '-i', required=True, help='输入目录路径，包含需要翻译的JSON文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录路径，存放翻译后的JSON文件')
    parser.add_argument('--api-key', help='阿里云通义千问API密钥，默认使用脚本中预设的密钥')
    parser.add_argument('--model', help='模型名称，支持通用模型(qwen-plus/qwen-turbo)和翻译专用模型(qwen-mt-plus/qwen-mt-turbo)，默认为"qwen-mt-plus"', default='qwen-mt-plus')
    parser.add_argument('--exclude', help='排除翻译的字段列表，以逗号分隔', default='')
    parser.add_argument('--pattern', help='JSON文件匹配模式，默认为"**/*.json"', default='**/*.json')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，显示更详细的日志')
    parser.add_argument('--delete-source', action='store_true', help='翻译完成后删除原始文件，防止重复翻译')

    global args, DEBUG
    args = parser.parse_args()
    DEBUG = args.debug
    
    # 设置API密钥
    if args.api_key:
        dashscope.api_key = args.api_key
    
    # 添加用户自定义的排除字段
    exclude_keys = EXCLUDE_KEYS.copy()
    if args.exclude:
        exclude_fields = args.exclude.split(',')
        for field in exclude_fields:
            exclude_keys.add(field.strip())
    
    try:
        # 执行目录处理
        process_directory(args.input, args.output, exclude_keys, args.model, args.pattern, args.delete_source)

        # 输出最终统计信息
        log_debug("=== 翻译完成统计 ===")
        log_debug(f"成功翻译缓存: {len(translation_cache)} 条")
        log_debug(f"失败缓存: {len(failed_cache)} 条")
        if failed_cache:
            log_debug("翻译失败的文本:")
            for failed_text in list(failed_cache)[:10]:  # 只显示前10个
                log_debug(f"  - '{failed_text}'", "WARNING")
            if len(failed_cache) > 10:
                log_debug(f"  ... 还有 {len(failed_cache) - 10} 个失败项", "WARNING")

    except KeyboardInterrupt:
        print("\n程序被用户中断！")
        log_debug(f"中断时统计 - 成功: {len(translation_cache)}, 失败: {len(failed_cache)}")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        log_debug(f"程序异常退出: {e}", "ERROR")
        if DEBUG:
            import traceback
            print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main() 