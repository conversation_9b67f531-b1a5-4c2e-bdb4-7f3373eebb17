# JSON 智能翻译器

一个基于阿里云通义千问大模型的 JSON 文件翻译工具，可以保留 JSON 结构和键名，只翻译字符串内容。

## 功能特点

- 递归处理任意深度嵌套的 JSON 结构
- 保留所有键名不变，只翻译字符串值
- 支持排除特定字段的翻译
- 使用缓存机制避免重复翻译相同内容
- 添加请求间隔以避免触发 API 限制
- 保持 JSON 输出格式和 UTF-8 编码

## 安装依赖

```bash
pip install dashscope argparse
```

## 使用方法

### 基本用法

```bash
python json_translator.py input.json
```

默认情况下，翻译后的文件将保存为 `translated_input.json`

### 指定输出文件

```bash
python json_translator.py input.json --output output.json
```

### 使用自定义 API 密钥

```bash
python json_translator.py input.json --api-key YOUR_API_KEY
```

## 配置说明

脚本中有几个关键配置：

1. **API 密钥**：默认使用代码中的预设密钥，也可以通过命令行参数指定

```python
API_KEY = "sk-a42b37159a09413ebeb7dcb7c3dade9d"
```

2. **排除翻译的字段**：可以在代码中修改 `EXCLUDE_KEYS` 集合

```python
EXCLUDE_KEYS = {"type", "style", "level", "src", "url", "scraped_at"}
```

3. **翻译间隔**：可以调整 `time.sleep()` 的参数值以控制 API 请求的频率

```python
time.sleep(0.5)  # 单位为秒
```

## 示例

### 输入 JSON

```json
{
  "type": "paragraph",
  "text": "This is a paragraph.",
  "title": "How much playtime does a baby need?"
}
```

### 输出 JSON

```json
{
  "type": "paragraph",
  "text": "这是一段文字。",
  "title": "婴儿需要多少游戏时间？"
}
```

## 注意事项

- 需要有效的阿里云通义千问 API 密钥
- 大量翻译可能会受到 API 调用限制
- 翻译质量依赖于通义千问模型的能力

## 进一步开发

如需进一步开发，可以：

1. 添加更多翻译模型选项
2. 优化翻译质量的提示词
3. 增加批量处理多个文件的功能
4. 添加更细粒度的翻译控制选项 