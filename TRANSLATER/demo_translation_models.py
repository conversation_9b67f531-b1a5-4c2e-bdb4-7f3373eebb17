#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
翻译专用模型演示脚本
展示不同模型的翻译效果对比
"""

import json
import tempfile
import os
import sys

def create_demo_data():
    """创建演示数据"""
    return {
        "title": "Pregnancy Symptoms Guide",
        "description": "A comprehensive guide to early pregnancy symptoms and signs",
        "medical_terms": [
            "Nausea",
            "Morning sickness", 
            "Fatigue",
            "Breast tenderness",
            "Missed menstrual period",
            "Implantation bleeding",
            "Frequent urination"
        ],
        "content": {
            "introduction": "Pregnancy symptoms can vary significantly from woman to woman and even from pregnancy to pregnancy.",
            "early_signs": [
                "Nausea and vomiting, especially in the morning",
                "Increased sensitivity to smells",
                "Food aversions or cravings",
                "Mood changes and emotional sensitivity",
                "Breast changes including tenderness and swelling"
            ]
        },
        "medical_disclaimer": "This information is for educational purposes only and should not replace professional medical advice."
    }

def run_translation_demo(model_name, description):
    """运行单个模型的翻译演示"""
    print(f"\n{'='*60}")
    print(f"🔬 测试模型: {model_name}")
    print(f"📝 描述: {description}")
    print(f"{'='*60}")
    
    # 创建临时目录和文件
    temp_dir = tempfile.mkdtemp(prefix=f"demo_{model_name.replace('-', '_')}_")
    input_dir = os.path.join(temp_dir, "input")
    output_dir = os.path.join(temp_dir, "output")
    
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建测试文件
        test_file = os.path.join(input_dir, "pregnancy_guide.json")
        test_data = create_demo_data()
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"📁 输入文件: {test_file}")
        print(f"📁 输出目录: {output_dir}")
        
        # 构建命令行参数
        cmd_args = [
            "--input", input_dir,
            "--output", output_dir,
            "--model", model_name,
            "--debug"
        ]
        
        # 模拟命令行参数
        original_argv = sys.argv.copy()
        sys.argv = ["json_translator_batch.py"] + cmd_args
        
        try:
            # 重新导入模块以清除之前的状态
            if 'json_translator_batch' in sys.modules:
                del sys.modules['json_translator_batch']
            
            import json_translator_batch
            
            print(f"🚀 开始翻译...")
            # 运行翻译
            json_translator_batch.main()
            
            # 检查结果
            output_file = os.path.join(output_dir, "pregnancy_guide.json")
            if os.path.exists(output_file):
                print(f"✅ 翻译完成!")
                
                # 读取并显示翻译结果
                with open(output_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                
                print(f"\n📊 翻译结果预览:")
                print(f"标题: {result.get('title', 'N/A')}")
                print(f"描述: {result.get('description', 'N/A')}")
                
                if 'medical_terms' in result:
                    print(f"\n🏥 医学术语翻译:")
                    for i, term in enumerate(result['medical_terms'][:5], 1):
                        print(f"  {i}. {term}")
                
                if 'content' in result and 'introduction' in result['content']:
                    print(f"\n📖 内容介绍:")
                    print(f"  {result['content']['introduction']}")
                
                if 'content' in result and 'early_signs' in result['content']:
                    print(f"\n🔍 早期症状:")
                    for i, sign in enumerate(result['content']['early_signs'][:3], 1):
                        print(f"  {i}. {sign}")
                
                return True
            else:
                print(f"❌ 翻译失败，未找到输出文件")
                return False
                
        except Exception as e:
            print(f"❌ 翻译过程出错: {e}")
            return False
        finally:
            # 恢复原始命令行参数
            sys.argv = original_argv
            
    finally:
        # 清理临时文件
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 临时目录已清理")
        except:
            print(f"⚠️ 警告: 无法清理临时目录 {temp_dir}")

def main():
    """主演示函数"""
    print("🎯 翻译专用模型效果演示")
    print("=" * 80)
    print("本演示将对比不同模型的翻译效果，特别是医学术语的翻译准确性")
    
    # 定义要测试的模型
    models = [
        ("qwen-mt-plus", "高质量翻译专用模型 - 推荐用于专业文档"),
        ("qwen-mt-turbo", "快速翻译专用模型 - 适合大批量翻译"),
        ("qwen-plus", "通用大语言模型 - 对比基准")
    ]
    
    results = {}
    
    for model, description in models:
        try:
            print(f"\n⏳ 准备测试 {model}...")
            results[model] = run_translation_demo(model, description)
        except Exception as e:
            print(f"❌ 测试模型 {model} 时出错: {e}")
            results[model] = False
    
    # 输出总结
    print(f"\n{'='*80}")
    print("📈 演示总结")
    print(f"{'='*80}")
    
    for model, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        model_type = "翻译专用" if model.startswith("qwen-mt") else "通用模型"
        print(f"{status} {model} ({model_type})")
    
    success_count = sum(results.values())
    total_count = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_count} 个模型演示成功")
    
    if success_count > 0:
        print(f"\n💡 建议:")
        print(f"  • 对于医学、法律等专业文档，推荐使用 qwen-mt-plus")
        print(f"  • 对于大批量快速翻译，推荐使用 qwen-mt-turbo") 
        print(f"  • 对于需要额外文本处理的场景，可使用通用模型")
    
    print(f"\n🔧 使用方法:")
    print(f"  python json_translator_batch.py --input 输入目录 --output 输出目录 --model qwen-mt-plus")

if __name__ == "__main__":
    main()
