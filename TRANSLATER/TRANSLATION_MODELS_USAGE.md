# 翻译专用模型使用说明

## 功能概述

脚本现已支持阿里云的翻译专用模型，包括 `qwen-mt-turbo` 和 `qwen-mt-plus`，这些模型专门针对翻译任务进行了优化，提供更高质量的翻译结果。

## 支持的模型

### 翻译专用模型
- **qwen-mt-plus**: 高质量翻译模型，翻译准确度更高
- **qwen-mt-turbo**: 快速翻译模型，响应速度更快

### 通用文本生成模型
- **qwen-plus**: 通用大语言模型，支持翻译功能
- **qwen-turbo**: 快速通用模型
- **qwen-max**: 最强通用模型

## 使用方法

### 基本语法

```bash
# 使用翻译专用模型（推荐）
python json_translator_batch.py --input <输入目录> --output <输出目录> --model qwen-mt-plus

# 使用快速翻译模型
python json_translator_batch.py --input <输入目录> --output <输出目录> --model qwen-mt-turbo
```

### 完整示例

```bash
# 高质量翻译（默认）
python json_translator_batch.py \
    --input json/ \
    --output translated/ \
    --model qwen-mt-plus \
    --debug

# 快速翻译
python json_translator_batch.py \
    --input json/ \
    --output translated/ \
    --model qwen-mt-turbo \
    --delete-source

# 使用通用模型对比
python json_translator_batch.py \
    --input json/ \
    --output translated_general/ \
    --model qwen-plus
```

## 技术实现

### 自动模型检测

脚本会自动检测使用的模型类型：

```python
def is_translation_model(model: str) -> bool:
    """判断是否为翻译专用模型"""
    translation_models = {'qwen-mt-turbo', 'qwen-mt-plus'}
    return model.lower() in translation_models
```

### API调用差异

#### 翻译专用模型
```python
response = Generation.call(
    model="qwen-mt-plus",
    input={
        "text": "Hello World"  # 直接传入原文本
    },
    parameters={
        "translation_options": {
            "source_language": "en",
            "target_language": "zh"
        }
    }
)
```

#### 通用模型
```python
response = Generation.call(
    model="qwen-plus",
    prompt="请将以下英文翻译成中文：Hello World",
    temperature=0.1,
    max_tokens=2048
)
```

### 响应处理

#### 翻译专用模型响应
```json
{
    "output": {
        "translation": "你好世界"
    }
}
```

#### 通用模型响应
```json
{
    "output": {
        "text": "你好世界"
    }
}
```

## 模型对比

| 特性 | qwen-mt-plus | qwen-mt-turbo | qwen-plus |
|------|-------------|---------------|-----------|
| 模型类型 | 翻译专用 | 翻译专用 | 通用模型 |
| 翻译质量 | 最高 | 高 | 中等 |
| 响应速度 | 中等 | 最快 | 快 |
| 专业术语 | 优秀 | 良好 | 一般 |
| 上下文理解 | 优秀 | 良好 | 良好 |
| 成本 | 中等 | 低 | 中等 |

## 使用建议

### 场景选择

1. **高质量翻译需求**
   ```bash
   # 医学、法律、技术文档等专业内容
   python json_translator_batch.py --model qwen-mt-plus
   ```

2. **大批量快速翻译**
   ```bash
   # 新闻、社交媒体等一般内容
   python json_translator_batch.py --model qwen-mt-turbo
   ```

3. **通用文本处理**
   ```bash
   # 需要额外文本处理功能时
   python json_translator_batch.py --model qwen-plus
   ```

### 性能优化

1. **批量处理优化**
   ```bash
   # 使用翻译专用模型 + 删除原文件
   python json_translator_batch.py \
       --model qwen-mt-plus \
       --delete-source \
       --pattern "**/*.json"
   ```

2. **调试和监控**
   ```bash
   # 启用详细日志
   python json_translator_batch.py \
       --model qwen-mt-plus \
       --debug
   ```

## 错误处理

### 常见错误

1. **缺少翻译配置参数**
   ```
   错误: Field required: parameters.translation_options
   解决: 脚本会自动添加翻译配置参数
   ```

2. **模型不支持**
   ```
   错误: Model not found
   解决: 检查模型名称是否正确
   ```

### 故障转移

脚本包含自动故障转移机制：
1. 优先使用简化API调用
2. 失败时自动切换到备用API
3. 翻译专用模型和通用模型都有对应的备用方案

## 调试信息

启用调试模式时，会显示详细的处理信息：

```
[2025-01-03 10:30:15] [INFO] 检测到翻译专用模型: qwen-mt-plus, 使用翻译API
[2025-01-03 10:30:15] [INFO] 调用简化API: model=qwen-mt-plus, prompt长度=0
[2025-01-03 10:30:16] [INFO] 翻译模型API成功: 'Nausea' -> '恶心'
```

## 测试功能

运行测试脚本验证功能：

```bash
python test_translation_models.py
```

测试脚本会：
1. 测试模型检测功能
2. 对比不同模型的翻译效果
3. 验证API调用的正确性

## 注意事项

1. **模型可用性**: 确保您的API密钥有权限访问翻译专用模型
2. **语言支持**: 当前配置为英文到中文翻译，可根据需要调整
3. **成本控制**: 翻译专用模型的计费可能与通用模型不同
4. **质量评估**: 建议先用小批量数据测试翻译质量
