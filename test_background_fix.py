#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：验证JSON翻译器的后台运行修复效果
"""

import os
import json
import tempfile
import subprocess
import time
import sys

def create_test_data():
    """创建测试用的JSON数据"""
    return {
        "title": "Test Document",
        "description": "This is a test document for translation",
        "content": {
            "introduction": "Welcome to our test",
            "sections": [
                {
                    "name": "Section 1",
                    "text": "This is the first section"
                },
                {
                    "name": "Section 2", 
                    "text": "This is the second section"
                }
            ]
        },
        "tags": ["test", "document", "sample"],
        "metadata": {
            "type": "test",
            "level": 1,
            "url": "https://example.com"
        }
    }

def test_background_execution():
    """测试后台运行功能"""
    print("=== 测试JSON翻译器后台运行修复 ===\n")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        log_file = os.path.join(temp_dir, "test_translator.log")
        
        os.makedirs(input_dir)
        os.makedirs(output_dir)
        
        # 创建测试文件
        test_file = os.path.join(input_dir, "test.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(create_test_data(), f, ensure_ascii=False, indent=2)
        
        print(f"✓ 测试文件已创建: {test_file}")
        print(f"✓ 输出目录: {output_dir}")
        print(f"✓ 日志文件: {log_file}")
        
        # 测试1: 直接运行（前台）
        print("\n--- 测试1: 前台运行 ---")
        try:
            cmd = [
                sys.executable, "json_translator_batch.py",
                "--input", input_dir,
                "--output", output_dir + "_foreground",
                "--debug",
                "--log-file", log_file + "_foreground"
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✓ 前台运行成功")
                if os.path.exists(log_file + "_foreground"):
                    print("✓ 日志文件已创建")
                    with open(log_file + "_foreground", 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        if "翻译完成" in log_content:
                            print("✓ 翻译任务完成")
                        else:
                            print("⚠ 翻译可能未完成")
            else:
                print(f"✗ 前台运行失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⚠ 前台运行超时（可能是API调用较慢）")
        except Exception as e:
            print(f"✗ 前台运行出错: {e}")
        
        # 测试2: 后台运行脚本
        print("\n--- 测试2: 后台运行脚本 ---")
        try:
            if os.path.exists("run_translator_background.sh"):
                cmd = [
                    "./run_translator_background.sh",
                    "--input", input_dir,
                    "--output", output_dir + "_background",
                    "--debug",
                    "--log-file", log_file + "_background"
                ]
                
                print(f"执行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print("✓ 后台脚本启动成功")
                    print("输出:", result.stdout)
                    
                    # 等待一段时间让翻译完成
                    print("等待翻译完成...")
                    time.sleep(10)
                    
                    # 检查状态
                    status_result = subprocess.run(
                        ["./run_translator_background.sh", "status"],
                        capture_output=True, text=True
                    )
                    print("状态检查:", status_result.stdout)
                    
                    # 停止后台进程
                    stop_result = subprocess.run(
                        ["./run_translator_background.sh", "stop"],
                        capture_output=True, text=True
                    )
                    print("停止结果:", stop_result.stdout)
                    
                else:
                    print(f"✗ 后台脚本启动失败: {result.stderr}")
            else:
                print("⚠ 后台运行脚本不存在，跳过测试")
                
        except Exception as e:
            print(f"✗ 后台脚本测试出错: {e}")
        
        # 测试3: nohup直接运行
        print("\n--- 测试3: nohup直接运行 ---")
        try:
            cmd = f"""nohup python3 json_translator_batch.py \
                --input {input_dir} \
                --output {output_dir}_nohup \
                --debug \
                --log-file {log_file}_nohup > {log_file}_nohup_output 2>&1 &"""
            
            print(f"执行命令: {cmd}")
            
            # 启动后台进程
            process = subprocess.Popen(
                cmd, shell=True, 
                stdout=subprocess.DEVNULL, 
                stderr=subprocess.DEVNULL
            )
            
            print(f"✓ nohup进程已启动 (PID: {process.pid})")
            
            # 等待一段时间
            time.sleep(5)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                print("✓ 进程仍在运行")
                
                # 等待更长时间让翻译完成
                print("等待翻译完成...")
                time.sleep(10)
                
                # 检查日志文件
                if os.path.exists(log_file + "_nohup"):
                    print("✓ nohup日志文件已创建")
                    with open(log_file + "_nohup", 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        print(f"日志内容预览:\n{log_content[:500]}...")
                
                # 终止进程
                try:
                    process.terminate()
                    process.wait(timeout=5)
                    print("✓ 进程已正常终止")
                except subprocess.TimeoutExpired:
                    process.kill()
                    print("⚠ 进程被强制终止")
                    
            else:
                print(f"✗ 进程已退出 (返回码: {process.returncode})")
                
        except Exception as e:
            print(f"✗ nohup测试出错: {e}")
        
        # 总结
        print("\n=== 测试总结 ===")
        print("1. 前台运行: 测试基本功能")
        print("2. 后台脚本: 测试进程管理和监控")
        print("3. nohup运行: 测试修复后的后台兼容性")
        print("\n如果所有测试都通过，说明后台运行问题已修复。")
        print("建议使用后台运行脚本进行生产环境部署。")

if __name__ == "__main__":
    test_background_execution()
