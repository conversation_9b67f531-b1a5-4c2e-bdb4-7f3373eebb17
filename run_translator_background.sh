#!/bin/bash

# JSON翻译器后台运行脚本
# 使用方法: ./run_translator_background.sh --input /path/to/input --output /path/to/output [其他参数]

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/json_translator_batch.py"

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "错误: 找不到Python脚本 $PYTHON_SCRIPT"
    exit 1
fi

# 设置默认值
LOG_FILE="json_translator_$(date +%Y%m%d_%H%M%S).log"
PID_FILE="json_translator.pid"

# 解析命令行参数以获取日志文件名
while [[ $# -gt 0 ]]; do
    case $1 in
        --log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        *)
            # 保留其他参数传递给Python脚本
            ARGS="$ARGS $1"
            shift
            ;;
    esac
done

# 如果没有指定日志文件，添加默认日志文件参数
if [[ "$ARGS" != *"--log-file"* ]]; then
    ARGS="$ARGS --log-file $LOG_FILE"
fi

echo "=== JSON翻译器后台运行脚本 ==="
echo "Python脚本: $PYTHON_SCRIPT"
echo "日志文件: $LOG_FILE"
echo "PID文件: $PID_FILE"
echo "参数: $ARGS"
echo ""

# 检查是否已有进程在运行
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p "$OLD_PID" > /dev/null 2>&1; then
        echo "警告: 检测到已有翻译进程在运行 (PID: $OLD_PID)"
        echo "如需停止现有进程，请运行: kill $OLD_PID"
        echo "或者删除PID文件: rm $PID_FILE"
        exit 1
    else
        echo "清理过期的PID文件: $PID_FILE"
        rm -f "$PID_FILE"
    fi
fi

# 创建启动函数
start_translator() {
    echo "正在启动JSON翻译器..."
    
    # 使用nohup在后台运行，重定向所有输出到日志文件
    nohup python3 "$PYTHON_SCRIPT" $ARGS > "$LOG_FILE" 2>&1 &
    
    # 获取进程ID
    TRANSLATOR_PID=$!
    
    # 保存PID到文件
    echo "$TRANSLATOR_PID" > "$PID_FILE"
    
    # 等待一小段时间检查进程是否成功启动
    sleep 2
    
    if ps -p "$TRANSLATOR_PID" > /dev/null 2>&1; then
        echo "✓ 翻译器已成功启动"
        echo "  进程ID: $TRANSLATOR_PID"
        echo "  日志文件: $LOG_FILE"
        echo "  PID文件: $PID_FILE"
        echo ""
        echo "监控命令:"
        echo "  查看日志: tail -f $LOG_FILE"
        echo "  检查进程: ps -p $TRANSLATOR_PID"
        echo "  停止进程: kill $TRANSLATOR_PID"
        echo ""
        
        # 显示最初几行日志
        echo "=== 最新日志 ==="
        if [ -f "$LOG_FILE" ]; then
            tail -n 10 "$LOG_FILE"
        fi
        
    else
        echo "✗ 翻译器启动失败"
        echo "请检查日志文件: $LOG_FILE"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 创建停止函数
stop_translator() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "正在停止翻译器进程 (PID: $PID)..."
            kill "$PID"
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    echo "✓ 翻译器已停止"
                    rm -f "$PID_FILE"
                    return 0
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制终止
            echo "强制终止进程..."
            kill -9 "$PID" 2>/dev/null
            rm -f "$PID_FILE"
            echo "✓ 翻译器已强制停止"
        else
            echo "进程 $PID 不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    else
        echo "未找到PID文件，翻译器可能未运行"
    fi
}

# 检查状态函数
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "✓ 翻译器正在运行 (PID: $PID)"
            echo "  日志文件: $LOG_FILE"
            
            # 显示最新日志
            if [ -f "$LOG_FILE" ]; then
                echo ""
                echo "=== 最新日志 (最后10行) ==="
                tail -n 10 "$LOG_FILE"
            fi
        else
            echo "✗ 翻译器未运行 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        echo "✗ 翻译器未运行"
    fi
}

# 主逻辑
if [ "$1" = "stop" ]; then
    stop_translator
elif [ "$1" = "status" ]; then
    check_status
elif [ "$1" = "restart" ]; then
    stop_translator
    sleep 2
    start_translator
else
    start_translator
fi
